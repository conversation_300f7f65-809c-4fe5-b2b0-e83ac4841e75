package com.pmw790.power.diagram;

import com.nomagic.magicdraw.core.Project;
import com.nomagic.magicdraw.openapi.uml.PresentationElementsManager;
import com.nomagic.magicdraw.openapi.uml.ReadOnlyElementException;
import com.nomagic.magicdraw.uml.symbols.DiagramPresentationElement;
import com.nomagic.magicdraw.uml.symbols.PresentationElement;
import com.nomagic.uml2.ext.jmi.helpers.ModelHelper;
import com.nomagic.uml2.ext.jmi.helpers.StereotypesHelper;
import com.nomagic.uml2.ext.magicdraw.classes.mdkernel.*;
import com.nomagic.uml2.ext.magicdraw.classes.mdkernel.Class;
import com.nomagic.uml2.ext.magicdraw.compositestructures.mdinternalstructures.ConnectableElement;
import com.nomagic.uml2.ext.magicdraw.compositestructures.mdinternalstructures.Connector;
import com.nomagic.uml2.ext.magicdraw.compositestructures.mdinternalstructures.ConnectorEnd;
import com.nomagic.uml2.ext.magicdraw.mdprofiles.Stereotype;
import com.pmw790.power.functions.Utilities;
import com.pmw790.power.functions.Utilities.ModelElements;
import com.pmw790.power.functions.SysMLStereotypes;
import com.pmw790.power.schema.BindingPattern;
import com.pmw790.power.schema.BindingSchema;
import com.pmw790.power.schema.BindingSchemaManager;

import java.util.*;

import static com.pmw790.power.functions.Utilities.Log;

/**
 * Manages creation and configuration of connectors in power diagrams.
 */
public class PowerConnectorManager {

    /**
     * Helper class to store connector specifications for deferred creation
     */
    private static class ConnectorSpec {
        private final Project project;
        private final Class owner;
        private final ConnectableElement sourceRole;
        private final Property sourcePart;
        private final ConnectableElement targetRole;
        private final Property targetPart;
        private final DiagramPresentationElement diagram;

        public ConnectorSpec(Project project, Class owner,
                            ConnectableElement sourceRole, Property sourcePart,
                            ConnectableElement targetRole, Property targetPart,
                            DiagramPresentationElement diagram) {
            this.project = project;
            this.owner = owner;
            this.sourceRole = sourceRole;
            this.sourcePart = sourcePart;
            this.targetRole = targetRole;
            this.targetPart = targetPart;
            this.diagram = diagram;
        }

        public Project getProject() { return project; }
        public Class getOwner() { return owner; }
        public ConnectableElement getSourceRole() { return sourceRole; }
        public Property getSourcePart() { return sourcePart; }
        public ConnectableElement getTargetRole() { return targetRole; }
        public Property getTargetPart() { return targetPart; }
        public DiagramPresentationElement getDiagram() { return diagram; }

        @Override
        public boolean equals(Object o) {
            if (this == o) return true;
            if (o == null || getClass() != o.getClass()) return false;
            ConnectorSpec that = (ConnectorSpec) o;
            return sourceRole.equals(that.sourceRole) &&
                   Objects.equals(sourcePart, that.sourcePart) &&
                   targetRole.equals(that.targetRole) &&
                   Objects.equals(targetPart, that.targetPart);
        }

        @Override
        public int hashCode() {
            return Objects.hash(sourceRole, sourcePart, targetRole, targetPart);
        }
    }

    /**
     * Helper class to store power connection information
     */
    private static class PowerConnection {
        private final Property powerProperty;
        private final Property partProperty;
        private final String elementName;

        public PowerConnection(Property powerProperty, Property partProperty, String elementName) {
            this.powerProperty = powerProperty;
            this.partProperty = partProperty;
            this.elementName = elementName;
        }

        public Property getPowerProperty() { return powerProperty; }
        public Property getPartProperty() { return partProperty; }
        public String getElementName() { return elementName; }
    }

    // Cache for connectors by owner to improve performance
    private static final Map<Class, List<Connector>> connectorCache = new WeakHashMap<>();
    private static final Map<String, Boolean> connectorExistenceCache = new HashMap<>();

    //--------------------------------------------------------------------------
    // MAIN METHODS
    //--------------------------------------------------------------------------

    /**
     * Creates binding connectors between elements in the diagram.
     */
    public static void createBindingConnectors(CabinetDiagramContext context, DiagramPresentationElement diagram) {
        Project project = context.getProject();
        Class cabinetBlock = context.getCabinetBlock();

        if (context.isRoomContext()) {
            Log("Creating binding connectors for cabinet " + context.getCabinetName() + " in room " + context.getRoomName());
        }

        for (String providerName : context.getProviders()) {
            processProvider(context, project, cabinetBlock, providerName, diagram);
        }
    }

    /**
     * Processes a single power provider, creating all necessary bindings
     */
    private static void processProvider(CabinetDiagramContext context, Project project,
                                        Class cabinetBlock, String providerName,
                                        DiagramPresentationElement diagram) {
        List<String> consumers = context.getConsumersForProvider(providerName);
        boolean isTopProvider = context.isTopProvider(providerName);

        // Skip child providers without consumers
        if (!isTopProvider && (consumers == null || consumers.isEmpty())) {
            return;
        }

        if (consumers == null) {
            consumers = new ArrayList<>();
        }

        Property providerPartProperty = context.getPartProperty(providerName);
        Map<String, Property> constraintProperties = context.getConstraintProperties(providerName);
        Map<String, Map<String, Property>> constraintPortsMap = buildConstraintPortsMap(context, constraintProperties, providerName);
        Map<String, Property> providerValueProperties = context.getValueProperties(providerName);
        Map<String, Map<String, Property>> consumerValueProperties = buildConsumerValueProperties(context, consumers);

        createSchemaBasedBindings(project, cabinetBlock, providerPartProperty, constraintPortsMap,
                providerValueProperties, consumerValueProperties, consumers, diagram, context);
    }

    //--------------------------------------------------------------------------
    // HELPER METHODS FOR DATA PREPARATION
    //--------------------------------------------------------------------------

    /**
     * Builds constraint ports map for a provider
     */
    private static Map<String, Map<String, Property>> buildConstraintPortsMap(
            CabinetDiagramContext context, Map<String, Property> constraintProperties, String providerName) {
        Map<String, Map<String, Property>> constraintPortsMap = new HashMap<>();
        for (String constraintType : constraintProperties.keySet()) {
            Map<String, Property> ports = context.getConstraintPorts(providerName, constraintType);
            if (ports != null && !ports.isEmpty()) {
                constraintPortsMap.put(constraintType, ports);
            }
        }
        return constraintPortsMap;
    }

    /**
     * Builds consumer value properties map
     */
    private static Map<String, Map<String, Property>> buildConsumerValueProperties(
            CabinetDiagramContext context, List<String> consumers) {
        Map<String, Map<String, Property>> consumerValueProperties = new HashMap<>();
        for (String consumerName : consumers) {
            Map<String, Property> consumerProps = context.getValueProperties(consumerName);
            if (consumerProps != null && !consumerProps.isEmpty()) {
                consumerValueProperties.put(consumerName, consumerProps);
            }
        }
        return consumerValueProperties;
    }

    /**
     * Finds Power Total constraint and its parameters
     */
    private static PowerTotalInfo findPowerTotalInfo(Map<String, Map<String, Property>> constraintPortsMap,
                                                     CabinetDiagramContext context, String providerName) {
        for (Map.Entry<String, Map<String, Property>> entry : constraintPortsMap.entrySet()) {
            String constraintType = entry.getKey();
            if (constraintNameMatches("Power Total", constraintType)) {
                Map<String, Property> constraintProperties = context.getConstraintProperties(providerName);
                Property powerTotalConstraint = constraintProperties.get(constraintType);
                Map<String, Property> powerTotalParams = entry.getValue();
                return new PowerTotalInfo(powerTotalConstraint, powerTotalParams);
            }
        }
        return null;
    }

    /**
     * Helper class for Power Total constraint information
     */
    private static class PowerTotalInfo {
        private final Property constraint;
        private final Map<String, Property> parameters;

        public PowerTotalInfo(Property constraint, Map<String, Property> parameters) {
            this.constraint = constraint;
            this.parameters = parameters;
        }

        public Property getConstraint() { return constraint; }
        public Map<String, Property> getParameters() { return parameters; }
        public Property getP1Port() { return parameters != null ? parameters.get("P1") : null; }
    }

    /**
     * Gets power property from element (total_power or power_consumption)
     */
    private static Property getPowerProperty(Map<String, Property> valueProperties) {
        if (valueProperties == null) return null;

        Property powerProp = valueProperties.get("total_power");
        if (powerProp == null) {
            powerProp = valueProperties.get("power_consumption");
        }
        return powerProp;
    }

    /**
     * Creates power connections for elements to Power_Total constraint
     */
    private static List<ConnectorSpec> createPowerConnections(Project project, Class cabinetBlock,
                                                              List<PowerConnection> connections, Property powerTotalConstraint, Property p1Port,
                                                              DiagramPresentationElement diagram) {

        List<ConnectorSpec> specs = new ArrayList<>();

        for (PowerConnection connection : connections) {
            if (connection.getPowerProperty() != null) {
                ConnectorSpec spec = createConnectorSpec(project, cabinetBlock,
                        connection.getPowerProperty(), connection.getPartProperty(),
                        p1Port, powerTotalConstraint, diagram);

                if (spec != null) {
                    specs.add(spec);
                }
            } else {
                Log("Warning: Could not find power property for " + connection.getElementName());
            }
        }

        return specs;
    }

    //--------------------------------------------------------------------------
    // SCHEMA-BASED BINDING METHODS
    //--------------------------------------------------------------------------

    /**
     * Creates binding connections based on the binding schema.
     */
    private static void createSchemaBasedBindings(Project project, Class cabinetBlock, Property providerBlock,
                                                  Map<String, Map<String, Property>> constraintPortsMap, Map<String, Property> providerValueProperties,
                                                  Map<String, Map<String, Property>> consumerValuePropertiesMap, List<String> consumerNames,
                                                  DiagramPresentationElement diagram, CabinetDiagramContext context) {

        List<ConnectorSpec> pendingConnectors = new ArrayList<>();
        String providerName = providerBlock.getName();
        boolean isTopProvider = context.isTopProvider(providerName);

        // Find Power Total constraint information
        PowerTotalInfo powerTotalInfo = findPowerTotalInfo(constraintPortsMap, context, providerName);

        // Handle provider connections
        if (powerTotalInfo != null) {
            // External load connection for all providers
            ConnectorSpec externalLoadSpec = connectExternalLoad(context, project, cabinetBlock,
                    providerName, powerTotalInfo, diagram);
            if (externalLoadSpec != null) {
                pendingConnectors.add(externalLoadSpec);
            }

            // For top providers, handle child provider and consumer connections
            if (isTopProvider) {
                // Child provider connections
                List<PowerConnection> childConnections = getChildProviderConnections(context, providerName);
                pendingConnectors.addAll(createPowerConnections(project, cabinetBlock, childConnections,
                        powerTotalInfo.getConstraint(), powerTotalInfo.getP1Port(), diagram));

                // Direct consumer connections
                List<PowerConnection> consumerConnections = getConsumerConnections(context, consumerNames);
                pendingConnectors.addAll(createPowerConnections(project, cabinetBlock, consumerConnections,
                        powerTotalInfo.getConstraint(), powerTotalInfo.getP1Port(), diagram));
            }
        }

        // Process schema patterns for the provider
        pendingConnectors.addAll(processSchemaPatterns(Utilities.CLASSIFIER_POWER_PROVIDER,
                cabinetBlock, project, providerBlock, constraintPortsMap, providerValueProperties,
                diagram, context, false));

        // Only process consumer schema patterns when there are actual consumers to process
        if (!consumerNames.isEmpty()) {
            pendingConnectors.addAll(processConsumerSchemaPatterns(consumerNames, context, cabinetBlock,
                    project, constraintPortsMap, consumerValuePropertiesMap, diagram));
        }

        // Create all connectors in batch
        if (!pendingConnectors.isEmpty()) {
            createConnectorsBatch(pendingConnectors);
        }
    }

    /**
     * Gets child provider power connections
     */
    private static List<PowerConnection> getChildProviderConnections(CabinetDiagramContext context, String topProviderName) {
        List<PowerConnection> connections = new ArrayList<>();
        List<String> childProviders = context.getChildProvidersForTop(topProviderName);

        if (childProviders != null) {
            for (String childName : childProviders) {
                Property childPartProperty = context.getPartProperty(childName);
                if (childPartProperty != null) {
                    Map<String, Property> childValueProps = context.getValueProperties(childName);
                    Property totalPowerProperty = getPowerProperty(childValueProps);

                    if (totalPowerProperty != null) {
                        connections.add(new PowerConnection(totalPowerProperty, childPartProperty, childName));
                    }
                }
            }
        }

        return connections;
    }

    /**
     * Gets consumer power connections
     */
    private static List<PowerConnection> getConsumerConnections(CabinetDiagramContext context, List<String> consumerNames) {
        List<PowerConnection> connections = new ArrayList<>();

        for (String consumerName : consumerNames) {
            Property consumerPartProperty = context.getPartProperty(consumerName);
            if (consumerPartProperty != null) {
                Map<String, Property> consumerValueProps = context.getValueProperties(consumerName);
                Property powerProperty = getPowerProperty(consumerValueProps);

                if (powerProperty != null) {
                    connections.add(new PowerConnection(powerProperty, consumerPartProperty, consumerName));
                }
            }
        }

        return connections;
    }

    /**
     * Connects external load property to Power_Total constraint
     */
    private static ConnectorSpec connectExternalLoad(CabinetDiagramContext context, Project project,
                                                     Class cabinetBlock, String providerName,
                                                     PowerTotalInfo powerTotalInfo, DiagramPresentationElement diagram) {

        Property externalLoadProperty = context.getExternalLoadProperty(providerName);
        if (externalLoadProperty == null) {
            externalLoadProperty = ModelElements.findPropertyByName(cabinetBlock, "external_load");
        }

        Property p1Port = powerTotalInfo.getP1Port();
        if (externalLoadProperty != null && p1Port != null) {
            return createConnectorSpec(project, cabinetBlock,
                    externalLoadProperty, null,
                    p1Port, powerTotalInfo.getConstraint(), diagram);
        }

        if (externalLoadProperty == null) {
            Log("Warning: external_load property not found for provider " + providerName);
        }

        return null;
    }

    /**
     * Processes schema patterns for a classifier
     */
    private static List<ConnectorSpec> processSchemaPatterns(String classifierType, Class cabinetBlock,
                                                             Project project, Property ownerBlock, Map<String, Map<String, Property>> constraintPortsMap,
                                                             Map<String, Property> valueProperties, DiagramPresentationElement diagram,
                                                             CabinetDiagramContext context, boolean isConsumer) {

        List<ConnectorSpec> specs = new ArrayList<>();
        BindingSchema schema = BindingSchemaManager.getInstance().getBindingSchema(classifierType);

        if (schema == null || schema.getPatternCount() == 0) {
            Log("Warning: No binding schema available for " + classifierType);
            return specs;
        }

        for (BindingPattern pattern : schema.getPatterns()) {
            try {
                BindingContext patternContext = new BindingContext(pattern, cabinetBlock, project, ownerBlock,
                        constraintPortsMap, valueProperties, diagram, context, isConsumer);

                ConnectorSpec spec = processPattern(pattern, patternContext);
                if (spec != null) {
                    specs.add(spec);
                }
            } catch (Exception e) {
                Log("Error processing pattern " + pattern + ": " + e.getMessage());
            }
        }

        return specs;
    }

    /**
     * Processes consumer schema patterns
     */
    private static List<ConnectorSpec> processConsumerSchemaPatterns(List<String> consumerNames,
                                                                     CabinetDiagramContext context, Class cabinetBlock, Project project,
                                                                     Map<String, Map<String, Property>> constraintPortsMap,
                                                                     Map<String, Map<String, Property>> consumerValuePropertiesMap,
                                                                     DiagramPresentationElement diagram) {

        List<ConnectorSpec> specs = new ArrayList<>();

        for (String consumerName : consumerNames) {
            Property consumerPart = context.getPartProperty(consumerName);
            if (consumerPart == null || !(consumerPart.getType() instanceof Class)) {
                continue;
            }

            Map<String, Property> consumerValueProps = consumerValuePropertiesMap.get(consumerName);
            specs.addAll(processSchemaPatterns(Utilities.CLASSIFIER_POWER_CONSUMER, cabinetBlock,
                    project, consumerPart, constraintPortsMap, consumerValueProps, diagram, context, true));
        }

        return specs;
    }

    /**
     * Processes a single pattern based on its type
     */
    private static ConnectorSpec processPattern(BindingPattern pattern, BindingContext patternContext) {
        if (pattern.isConstraintToConstraint() && pattern.hasTargetProperty()) {
            return processConstraintToPropertyPattern(patternContext);
        } else if (pattern.isConstraintToConstraint() && !pattern.hasTargetProperty()) {
            return processConstraintToConstraintPattern(patternContext);
        } else if (!pattern.isConstraintToConstraint()) {
            return normalizeAndProcessPattern(pattern, patternContext);
        }
        return null;
    }

    /**
     * Normalizes and processes a property-to-constraint pattern
     */
    private static ConnectorSpec normalizeAndProcessPattern(BindingPattern pattern, BindingContext patternContext) {
        BindingPattern normalizedPattern = BindingPattern.createConstraintToPropertyPattern(
                pattern.getTargetConstraint(), pattern.getTargetParameter(), pattern.getSourceProperty());

        BindingContext normalizedContext = new BindingContext(normalizedPattern, patternContext.getCabinetBlock(),
                patternContext.getProject(), patternContext.getOwnerBlock(), patternContext.getConstraintPortsMap(),
                patternContext.getValueProperties(), patternContext.getDiagram(), patternContext.getContext(),
                patternContext.isConsumer());

        return processConstraintToPropertyPattern(normalizedContext);
    }

    //--------------------------------------------------------------------------
    // PATTERN PROCESSING METHODS
    //--------------------------------------------------------------------------

    /**
     * Processes a constraint-to-property binding pattern.
     */
    private static ConnectorSpec processConstraintToPropertyPattern(BindingContext bindingContext) {
        BindingPattern pattern = bindingContext.getPattern();
        String sourceConstraintName = pattern.getSourceConstraint();
        String sourceParameterName = pattern.getSourceParameter();
        String targetPropertyName = pattern.getTargetProperty();

        ConstraintPortInfo constraintInfo = findConstraintPort(sourceConstraintName, sourceParameterName, bindingContext);
        Property sourceConstraintProperty = constraintInfo.constraintProperty;
        Property sourcePort = constraintInfo.portProperty;
        Property targetProperty = bindingContext.getValueProperties().get(targetPropertyName);

        if (sourceConstraintProperty != null && sourcePort != null && targetProperty != null) {
            if (bindingContext.isConsumer()) {
                return createConnectorSpec(bindingContext.getProject(), bindingContext.getCabinetBlock(),
                        targetProperty, bindingContext.getOwnerBlock(),
                        sourcePort, sourceConstraintProperty, bindingContext.getDiagram());
            } else {
                return createConnectorSpec(bindingContext.getProject(), bindingContext.getCabinetBlock(),
                        sourcePort, sourceConstraintProperty,
                        targetProperty, bindingContext.getOwnerBlock(), bindingContext.getDiagram());
            }
        } else {
            logMissingElements("constraint-to-property", sourceConstraintName, sourceParameterName,
                    targetPropertyName, sourceConstraintProperty, sourcePort, targetProperty);
        }

        return null;
    }

    /**
     * Processes a constraint-to-constraint binding pattern.
     */
    private static ConnectorSpec processConstraintToConstraintPattern(BindingContext bindingContext) {
        BindingPattern pattern = bindingContext.getPattern();

        ConstraintPortInfo sourceInfo = findConstraintPort(pattern.getSourceConstraint(),
                pattern.getSourceParameter(), bindingContext);
        ConstraintPortInfo targetInfo = findConstraintPort(pattern.getTargetConstraint(),
                pattern.getTargetParameter(), bindingContext);

        if (sourceInfo.constraintProperty != null && sourceInfo.portProperty != null &&
                targetInfo.constraintProperty != null && targetInfo.portProperty != null) {

            return createConnectorSpec(bindingContext.getProject(), bindingContext.getCabinetBlock(),
                    sourceInfo.portProperty, sourceInfo.constraintProperty,
                    targetInfo.portProperty, targetInfo.constraintProperty, bindingContext.getDiagram());
        } else {
            logMissingConstraintElements("constraint-to-constraint",
                    pattern.getSourceConstraint(), pattern.getSourceParameter(),
                    pattern.getTargetConstraint(), pattern.getTargetParameter(),
                    sourceInfo.constraintProperty, sourceInfo.portProperty,
                    targetInfo.constraintProperty, targetInfo.portProperty);
        }

        return null;
    }

    /**
     * Helper class to store constraint property and port information
     */
    private static class ConstraintPortInfo {
        public final Property constraintProperty;
        public final Property portProperty;

        public ConstraintPortInfo(Property constraintProperty, Property portProperty) {
            this.constraintProperty = constraintProperty;
            this.portProperty = portProperty;
        }
    }

    /**
     * Finds a constraint property and its port
     */
    private static ConstraintPortInfo findConstraintPort(String constraintName, String parameterName,
                                                         BindingContext bindingContext) {
        Property constraintProperty = null;
        Property portProperty = null;

        for (Map.Entry<String, Map<String, Property>> entry : bindingContext.getConstraintPortsMap().entrySet()) {
            String constraintType = entry.getKey();
            if (constraintNameMatches(constraintName, constraintType)) {
                // For consumers, use the provider that this consumer is connected to
                // For providers, use the provider's own name
                String providerName;
                if (bindingContext.isConsumer()) {
                    // Get the provider that this consumer is connected to
                    String consumerName = bindingContext.getOwnerBlock().getName();
                    providerName = findProviderForConsumer(bindingContext.getContext(), consumerName);
                } else {
                    providerName = bindingContext.getOwnerBlock().getName();
                }

                Map<String, Property> constraintProperties = bindingContext.getContext().getConstraintProperties(providerName);
                constraintProperty = constraintProperties.get(constraintType);
                Map<String, Property> ports = entry.getValue();
                portProperty = ports.get(parameterName);
                break;
            }
        }

        return new ConstraintPortInfo(constraintProperty, portProperty);
    }

    //--------------------------------------------------------------------------
    // CONNECTOR CREATION METHODS
    //--------------------------------------------------------------------------

    /**
     * Creates a connector specification for deferred creation
     */
    private static ConnectorSpec createConnectorSpec(Project project, Class owner, ConnectableElement sourceRole,
                                                     Property sourcePart, ConnectableElement targetRole, Property targetPart,
                                                     DiagramPresentationElement diagram) {
        if (sourceRole == null || targetRole == null) {
            return null;
        }
        return new ConnectorSpec(project, owner, sourceRole, sourcePart, targetRole, targetPart, diagram);
    }

    /**
     * Creates connectors in batch from specifications
     */
    private static void createConnectorsBatch(List<ConnectorSpec> specs) {
        if (specs == null || specs.isEmpty()) {
            return;
        }

        Set<ConnectorSpec> uniqueSpecs = new HashSet<>(specs);

        for (ConnectorSpec spec : uniqueSpecs) {
            try {
                if (connectorExists(spec.getOwner(), spec.getSourceRole(), spec.getSourcePart(),
                        spec.getTargetRole(), spec.getTargetPart())) {
                    continue;
                }

                Connector connector = createModelConnector(spec.getProject(), spec.getOwner(),
                        spec.getSourceRole(), spec.getSourcePart(),
                        spec.getTargetRole(), spec.getTargetPart());
                if (connector == null) {
                    continue;
                }

                createDiagramConnector(spec.getDiagram(), connector, spec.getOwner(),
                        spec.getSourceRole(), spec.getSourcePart(),
                        spec.getTargetRole(), spec.getTargetPart());

            } catch (Exception e) {
                Log("Error creating connector from spec: " + e.getMessage());
            }
        }
    }

    /**
     * Creates the connector in the model
     */
    private static Connector createModelConnector(Project project, Class owner,
                                                  ConnectableElement sourceRole, Property sourcePart,
                                                  ConnectableElement targetRole, Property targetPart) {
        Connector connector = project.getElementsFactory().createConnectorInstance();
        connector.setOwner(owner);

        ModelHelper.setClientElement(connector, sourceRole);
        ModelHelper.setSupplierElement(connector, targetRole);

        Stereotype nestedConnectorEndStereotype = SysMLStereotypes.getNestedConnectorEndStereotype();
        if (nestedConnectorEndStereotype == null) {
            Log("Error: NestedConnectorEnd stereotype not found");
            return null;
        }

        // Configure source end
        ConnectorEnd sourceEnd = ModelHelper.getFirstEnd(connector);
        StereotypesHelper.addStereotype(sourceEnd, nestedConnectorEndStereotype);
        StereotypesHelper.setStereotypePropertyValue(sourceEnd, nestedConnectorEndStereotype, "propertyPath", sourcePart);

        // Configure target end
        ConnectorEnd targetEnd = ModelHelper.getSecondEnd(connector);
        StereotypesHelper.addStereotype(targetEnd, nestedConnectorEndStereotype);
        StereotypesHelper.setStereotypePropertyValue(targetEnd, nestedConnectorEndStereotype, "propertyPath", targetPart);

        // Add binding stereotype
        Stereotype bindingStereotype = StereotypesHelper.getStereotype(project, "BindingConnector",
                SysMLStereotypes.getSysMLProfile());
        if (bindingStereotype != null) {
            StereotypesHelper.addStereotype(connector, bindingStereotype);
        }

        // Clear caches
        connectorCache.remove(owner);
        connectorExistenceCache.clear();

        return connector;
    }

    /**
     * Creates the visual representation of the connector in the diagram
     */
    private static void createDiagramConnector(DiagramPresentationElement diagram, Connector connector,
                                               Class owner, ConnectableElement sourceRole, Property sourcePart,
                                               ConnectableElement targetRole, Property targetPart)
            throws ReadOnlyElementException {
        PresentationElement sourceRolePE = findPresentationElementForConnectorEnd(diagram, owner, sourceRole, sourcePart);
        PresentationElement targetRolePE = findPresentationElementForConnectorEnd(diagram, owner, targetRole, targetPart);

        if (sourceRolePE != null && targetRolePE != null) {
            PresentationElementsManager.getInstance().createPathElement(connector, sourceRolePE, targetRolePE);
        } else {
            String sourceDesc = sourceRole.getName();
            String targetDesc = targetRole.getName();

            if (sourceRolePE == null) {
                Log("Warning: Could not find presentation element for " + sourceDesc +
                        (sourcePart == null ? " (direct property)" : " in " + sourcePart.getName()));
            }
            if (targetRolePE == null) {
                Log("Warning: Could not find presentation element for " + targetDesc +
                        (targetPart == null ? " (direct property)" : " in " + targetPart.getName()));
            }
            Log("Created connector in model from " + sourceDesc + " to " + targetDesc +
                    ", but it won't be displayed in diagram");
        }
    }

    //--------------------------------------------------------------------------
    // UTILITY METHODS
    //--------------------------------------------------------------------------

    /**
     * Finds the presentation element for a connector endpoint
     */
    private static PresentationElement findPresentationElementForConnectorEnd(DiagramPresentationElement diagram,
                                                                              Class owner, Element role, Property part) {
        if (part == null) {
            PresentationElement rolePE = findDirectPropertyOnDiagram(diagram, role);
            if (rolePE == null) {
                PresentationElement ownerPE = findPresentationElement(diagram, owner);
                if (ownerPE != null) {
                    rolePE = findNestedPresentationElement(ownerPE, role);
                }
            }
            return rolePE;
        } else {
            PresentationElement partPE = findPresentationElement(diagram, part);
            if (partPE != null) {
                return findNestedPresentationElement(partPE, role);
            }
        }
        return null;
    }

    private static PresentationElement findDirectPropertyOnDiagram(DiagramPresentationElement diagram, Element property) {
        for (PresentationElement pe : diagram.getPresentationElements()) {
            if (pe.getElement() == property) {
                return pe;
            }
        }
        return null;
    }

    private static PresentationElement findPresentationElement(DiagramPresentationElement diagram, Element element) {
        for (PresentationElement pe : diagram.getPresentationElements()) {
            if (pe.getElement() == element) {
                return pe;
            }
        }
        return null;
    }

    private static PresentationElement findNestedPresentationElement(PresentationElement container, Element nestedElement) {
        // Search direct children first
        for (PresentationElement pe : container.getPresentationElements()) {
            if (pe.getElement() == nestedElement) {
                return pe;
            }
        }

        // If not found, search recursively in all children
        for (PresentationElement child : container.getPresentationElements()) {
            PresentationElement result = findNestedPresentationElement(child, nestedElement);
            if (result != null) {
                return result;
            }
        }

        return null;
    }

    /**
     * Gets cached connectors for an owner class
     */
    private static List<Connector> getCachedConnectorsForOwner(Class owner) {
        if (connectorCache.containsKey(owner)) {
            return connectorCache.get(owner);
        }

        List<Connector> connectors = new ArrayList<>();
        for (Element element : owner.getOwnedElement()) {
            if (element instanceof Connector) {
                connectors.add((Connector) element);
            }
        }

        connectorCache.put(owner, connectors);
        return connectors;
    }

    /**
     * Compares property paths with proper null handling
     */
    private static boolean comparePropertyPath(ConnectorEnd end, Stereotype nestedConnectorEndStereotype,
                                               Property part, boolean isDirectProperty) {
        List<?> pathValues = StereotypesHelper.getStereotypePropertyValue(
                end, nestedConnectorEndStereotype, "propertyPath");

        if (isDirectProperty) {
            boolean hasNullFirstElement = (pathValues != null && !pathValues.isEmpty() && pathValues.get(0) == null);
            return (pathValues == null || pathValues.isEmpty() || hasNullFirstElement);
        }

        boolean hasValue = (pathValues != null && !pathValues.isEmpty());
        if (!hasValue) return false;

        // Try reference equality first
        if (pathValues.get(0) == part) return true;

        // Fall back to ID comparison if needed
        if (pathValues.get(0) instanceof Element && part instanceof Element) {
            try {
                Element pathElement = (Element) pathValues.get(0);
                String pathId = pathElement.getID();
                String partId = ((Element) part).getID();
                return (pathId != null && partId != null && pathId.equals(partId));
            } catch (Exception e) {
                return false;
            }
        }

        return false;
    }

    /**
     * Checks if a binding connector already exists between the specified elements
     */
    private static boolean connectorExists(Class owner, ConnectableElement sourceRole,
                                           Property sourcePart, ConnectableElement targetRole,
                                           Property targetPart) {
        if (owner == null || sourceRole == null || targetRole == null) {
            return false;
        }

        final boolean isDirectSourceProperty = (sourcePart == null);
        final boolean isDirectTargetProperty = (targetPart == null);

        // Create cache key
        String cacheKey = null;
        try {
            String ownerId = owner.getID();
            String sourceRoleId = ((Element)sourceRole).getID();
            String targetRoleId = ((Element)targetRole).getID();
            String sourcePartId = (sourcePart != null) ? ((Element)sourcePart).getID() : "null";
            String targetPartId = (targetPart != null) ? ((Element)targetPart).getID() : "null";

            cacheKey = ownerId + "_" + sourceRoleId + "_" + sourcePartId + "_" + targetRoleId + "_" + targetPartId;

            if (connectorExistenceCache.containsKey(cacheKey)) {
                return connectorExistenceCache.get(cacheKey);
            }
        } catch (Exception e) {
            // Continue with normal check if cache key creation fails
        }

        List<Connector> connectors = getCachedConnectorsForOwner(owner);
        Stereotype nestedConnectorEndStereotype = SysMLStereotypes.getNestedConnectorEndStereotype();
        if (nestedConnectorEndStereotype == null) {
            if (cacheKey != null) connectorExistenceCache.put(cacheKey, false);
            return false;
        }

        // Prepare IDs for comparison
        String sourceRoleId = null;
        String targetRoleId = null;
        try {
            sourceRoleId = ((Element)sourceRole).getID();
            targetRoleId = ((Element)targetRole).getID();
        } catch (Exception e) {
            // Fall back to reference equality if ID retrieval fails
        }

        // Check all connectors
        for (Connector existingConnector : connectors) {
            ConnectorEnd sourceEnd = ModelHelper.getFirstEnd(existingConnector);
            ConnectorEnd targetEnd = ModelHelper.getSecondEnd(existingConnector);

            if (sourceEnd == null || targetEnd == null) continue;

            Element existingSourceRole = ModelHelper.getClientElement(existingConnector);
            Element existingTargetRole = ModelHelper.getSupplierElement(existingConnector);

            if (existingSourceRole == null || existingTargetRole == null) continue;

            // Check role matches
            boolean sourceRoleMatches = (existingSourceRole == sourceRole);
            boolean targetRoleMatches = (existingTargetRole == targetRole);

            // Try ID comparison if reference equality failed
            if (!sourceRoleMatches && sourceRoleId != null) {
                try {
                    String existingSourceRoleId = existingSourceRole.getID();
                    sourceRoleMatches = (existingSourceRoleId != null && sourceRoleId.equals(existingSourceRoleId));
                } catch (Exception e) {
                    // Continue if ID comparison fails
                }
            }

            if (!targetRoleMatches && targetRoleId != null) {
                try {
                    String existingTargetRoleId = existingTargetRole.getID();
                    targetRoleMatches = (existingTargetRoleId != null && targetRoleId.equals(existingTargetRoleId));
                } catch (Exception e) {
                    // Continue if ID comparison fails
                }
            }

            if (!sourceRoleMatches || !targetRoleMatches) {
                continue;
            }

            // Check property paths
            boolean sourceMatches = comparePropertyPath(sourceEnd, nestedConnectorEndStereotype,
                    sourcePart, isDirectSourceProperty);

            if (sourceMatches) {
                boolean targetMatches = comparePropertyPath(targetEnd, nestedConnectorEndStereotype,
                        targetPart, isDirectTargetProperty);

                if (targetMatches) {
                    if (cacheKey != null) connectorExistenceCache.put(cacheKey, true);
                    return true;
                }
            }
        }

        if (cacheKey != null) connectorExistenceCache.put(cacheKey, false);
        return false;
    }

    /**
     * Checks if a constraint name matches a class name
     */
    private static boolean constraintNameMatches(String patternName, String className) {
        return BindingSchemaManager.getInstance().constraintNameMatches(patternName, className);
    }

    /**
     * Logs missing elements for binding creation
     */
    private static void logMissingElements(String bindingType, String constraintName, String parameterName,
                                           String propertyName, Property constraintProperty, Property portProperty, Property targetProperty) {
        Log("Warning: Could not find all elements for " + bindingType + " binding: " +
                constraintName + "." + parameterName + " -> " + propertyName);
        if (constraintProperty == null) Log("  Missing source constraint property");
        if (portProperty == null) Log("  Missing source port: " + parameterName);
        if (targetProperty == null) Log("  Missing target property: " + propertyName);
    }

    /**
     * Logs missing elements for constraint-to-constraint binding creation
     */
    private static void logMissingConstraintElements(String bindingType,
                                                     String sourceConstraintName, String sourceParameterName,
                                                     String targetConstraintName, String targetParameterName,
                                                     Property sourceConstraintProperty, Property sourcePort,
                                                     Property targetConstraintProperty, Property targetPort) {

        Log("Warning: Could not find all elements for " + bindingType + " binding: " +
                sourceConstraintName + "." + sourceParameterName + " -> " +
                targetConstraintName + "." + targetParameterName);

        if (sourceConstraintProperty == null) Log("  Missing source constraint property: " + sourceConstraintName);
        if (sourcePort == null) Log("  Missing source port: " + sourceParameterName);
        if (targetConstraintProperty == null) Log("  Missing target constraint property: " + targetConstraintName);
        if (targetPort == null) Log("  Missing target port: " + targetParameterName);
    }

    /**
     * Clears the connector caches
     */
    public static void clearConnectorCache() {
        connectorCache.clear();
        connectorExistenceCache.clear();
    }

    /**
     * Finds the appropriate provider for a consumer
     * @param context The cabinet diagram context
     * @param consumerName The name of the consumer
     * @return The name of the provider that this consumer should be connected to
     */
    private static String findProviderForConsumer(CabinetDiagramContext context, String consumerName) {
        // Get all providers in the cabinet
        List<String> providers = context.getProviders();
        if (providers.isEmpty()) {
            return null;
        }

        // First, check if there's a top provider that has this consumer
        List<String> topProviders = context.getTopProviders();
        for (String topProvider : topProviders) {
            List<String> consumers = context.getConsumersForProvider(topProvider);
            if (consumers.contains(consumerName)) {
                return topProvider;
            }
        }

        // If not found in top providers, check child providers
        List<String> childProviders = context.getChildProviders();
        for (String childProvider : childProviders) {
            List<String> consumers = context.getConsumersForProvider(childProvider);
            if (consumers.contains(consumerName)) {
                return childProvider;
            }
        }

        // If no specific provider found, default to the first provider
        return providers.get(0);
    }
}